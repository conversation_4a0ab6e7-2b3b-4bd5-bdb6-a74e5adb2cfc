import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/list',
  save='/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/add',
  edit='/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/edit',
  deleteOne = '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/delete',
  deleteBatch = '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/deleteBatch',
  importExcel = '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/importExcel',
  exportXls = '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/exportXls',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 查询模板详情
 * @param id 模板ID
 */
export const queryById = (id: string) => {
  return defHttp.get({
    url: '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/queryById',
    params: { id }
  });
}

/**
 * 激活模板
 * @param templateId 模板ID
 */
export const activateTemplate = (templateId: string) => {
  return defHttp.post({
    url: '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/activate',
    params: { templateId }
  }, {
    joinParamsToUrl: true,
    successMessageMode: 'none' // 禁用自动成功提示，由业务代码处理
  });
}

/**
 * 检查当前用户所在部门是否有激活的模板
 */
export const checkActiveTemplate = () => {
  return defHttp.get({
    url: '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/checkActiveTemplate'
  });
}
